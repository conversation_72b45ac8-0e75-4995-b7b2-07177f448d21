import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:mialamobile/components/search_input.dart';
import 'package:mialamobile/dashboard/home/<USER>/notification_box.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/models/notification.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  TextEditingController searchController = TextEditingController();
  List<NotificationModel> filteredNotifications = [];
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeNotifications();
    });
  }

  Future<void> _initializeNotifications() async {
    if (!_isInitialized) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.fetchNotifications();
      _isInitialized = true;
    }
  }

  void _filterNotifications(String query) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    setState(() {
      filteredNotifications = authProvider.filterNotifications(query);
    });
  }

  Future<void> _markNotificationAsRead(int notificationId) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.markNotificationAsRead(notificationId);
  }

  Future<void> _markAllAsRead() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final success = await authProvider.markAllNotificationsAsRead();

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'All notifications marked as read',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: const Color(0xffB10303),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _refreshNotifications() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.fetchNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff121212),
      body: SafeArea(
        child: Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            final notifications = searchController.text.isEmpty
                ? authProvider.notifications
                : authProvider.filterNotifications(searchController.text);

            return RefreshIndicator(
              onRefresh: _refreshNotifications,
              backgroundColor: const Color(0xff1E1E1E),
              color: const Color(0xffB10303),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: 16, right: 16, bottom: 32, top: 16),
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: SvgPicture.asset('assets/icons/cancel.svg'),
                        ),
                        Text(
                          'Notifications',
                          style: GoogleFonts.poppins(
                              textStyle: const TextStyle(
                                  color: Color(0xffFFFFFF),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500)),
                        ),
                        PopupMenuButton<String>(
                          color: const Color(0xff1E1E1E),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                            side: const BorderSide(
                                color: Color(0xff1E1E1E)), // Added border
                          ),
                          icon: SvgPicture.asset(
                              'assets/icons/more_vertical.svg'),
                          itemBuilder: (BuildContext context) =>
                              <PopupMenuEntry<String>>[
                            PopupMenuItem<String>(
                              value: 'mark_all_read',
                              height: 37.0, // Added height
                              child: Center(
                                child: Text(
                                  'Mark all as read',
                                  style: GoogleFonts.poppins(
                                    textStyle: const TextStyle(
                                      color: Color(0xffFFFFFF),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                          onSelected: (String value) {
                            if (value == 'mark_all_read') {
                              _markAllAsRead();
                            }
                          },
                        ),
                      ],
                    ),
                    const Gap(23),
                    // Search input with both prefix and suffix icons
                    SearchInput(
                      focusedBorderColor: Colors.transparent,
                      hintText: '',
                      controller: searchController,
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: SvgPicture.asset(
                          'assets/icons/preference.svg',
                        ),
                      ),
                      suffixIcon: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: SvgPicture.asset(
                          'assets/icons/search.svg',
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          // Trigger rebuild to update filtered notifications
                        });
                      },
                    ),
                    const Gap(24),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text('Today',
                          style: GoogleFonts.poppins(
                              textStyle: const TextStyle(
                                  color: Color(0xffFFFFFF),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500))),
                    ),
                    const Gap(15),
                    // Notification list with loading and error states
                    if (authProvider.isLoadingNotifications)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.0),
                          child: CircularProgressIndicator(
                            color: Color(0xffB10303),
                          ),
                        ),
                      )
                    else if (authProvider.notificationsError != null)
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Column(
                            children: [
                              Text(
                                'Error loading notifications',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const Gap(8),
                              Text(
                                authProvider.notificationsError!,
                                style: GoogleFonts.poppins(
                                  color: const Color(0xff8C8C8C),
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const Gap(16),
                              ElevatedButton(
                                onPressed: _refreshNotifications,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xffB10303),
                                ),
                                child: Text(
                                  'Retry',
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    else if (notifications.isEmpty)
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Column(
                            children: [
                              SvgPicture.asset(
                                'assets/icons/notification.svg',
                                width: 64,
                                height: 64,
                              ),
                              const Gap(16),
                              Text(
                                'No notifications yet',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const Gap(8),
                              Text(
                                'You\'ll see your notifications here when you receive them',
                                style: GoogleFonts.poppins(
                                  color: const Color(0xff8C8C8C),
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      )
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: notifications.length,
                        itemBuilder: (context, index) {
                          final notification = notifications[index];
                          return NotificationBox(
                            iconPath: notification.iconPath,
                            title: notification.message,
                            subtitle: 'Type: ${notification.type}',
                            time: notification.formattedTime,
                            isRead: notification.read,
                            onTap: () =>
                                _markNotificationAsRead(notification.id),
                          );
                        },
                      ),
                  ]),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
