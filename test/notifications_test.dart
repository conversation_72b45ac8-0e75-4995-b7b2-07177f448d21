import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mialamobile/models/notification.dart';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/api/api_service.dart';
import 'package:mialamobile/api/models/user_model.dart';

// Generate mocks
@GenerateMocks([ApiService])
import 'notifications_test.mocks.dart';

void main() {
  group('NotificationModel Tests', () {
    test('should create NotificationModel from JSON', () {
      // Arrange
      final json = {
        'id': 1,
        'recipientId': 123,
        'message': 'Test notification message',
        'read': false,
        'createdAt': [2024, 1, 15, 10, 30, 0, 0],
        'type': 'DELIVERY_ASSIGNED',
      };

      // Act
      final notification = NotificationModel.fromJson(json);

      // Assert
      expect(notification.id, 1);
      expect(notification.recipientId, 123);
      expect(notification.message, 'Test notification message');
      expect(notification.read, false);
      expect(notification.type, 'DELIVERY_ASSIGNED');
      expect(notification.createdAt, [2024, 1, 15, 10, 30, 0, 0]);
    });

    test('should format time correctly', () {
      // Arrange
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 14, 30, 0, 0], // 2:30 PM
        type: 'TEST',
      );

      // Act & Assert
      expect(notification.formattedTime, '02:30pm');
    });

    test('should format date correctly for today', () {
      // Arrange
      final now = DateTime.now();
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [now.year, now.month, now.day, 10, 30, 0, 0],
        type: 'TEST',
      );

      // Act & Assert
      expect(notification.formattedDate, 'Today');
    });

    test('should return correct icon path for notification type', () {
      // Arrange
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'DELIVERY_ASSIGNED',
      );

      // Act & Assert
      expect(notification.iconPath, 'assets/icons/order.svg');
    });

    test('should create copy with updated read status', () {
      // Arrange
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'TEST',
      );

      // Act
      final updatedNotification = notification.copyWith(read: true);

      // Assert
      expect(updatedNotification.read, true);
      expect(updatedNotification.id, notification.id);
      expect(updatedNotification.message, notification.message);
    });
  });

  group('AuthProvider Notifications Tests', () {
    late AuthProvider authProvider;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider();
      authProvider.apiService = mockApiService;
    });

    test('should fetch notifications successfully', () async {
      // Arrange
      final user = User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user); // We'll need to add this method

      final mockResponse = {
        'responseCode': '00',
        'responseMsg': 'Success',
        'data': [
          {
            'id': 1,
            'recipientId': 123,
            'message': 'Test notification',
            'read': false,
            'createdAt': [2024, 1, 15, 10, 30, 0, 0],
            'type': 'DELIVERY_ASSIGNED',
          }
        ]
      };

      when(mockApiService.get(any, requiresAuth: true))
          .thenAnswer((_) async => ApiResponse(success: true, data: mockResponse));

      // Act
      final result = await authProvider.fetchNotifications();

      // Assert
      expect(result, true);
      expect(authProvider.notifications.length, 1);
      expect(authProvider.notifications.first.message, 'Test notification');
    });

    test('should handle fetch notifications error', () async {
      // Arrange
      final user = User(userId: 123, userRole: 'rider', email: '<EMAIL>');
      authProvider.setUserForTesting(user);

      when(mockApiService.get(any, requiresAuth: true))
          .thenAnswer((_) async => ApiResponse(success: false, error: 'Network error'));

      // Act
      final result = await authProvider.fetchNotifications();

      // Assert
      expect(result, false);
      expect(authProvider.notificationsError, 'Network error');
    });

    test('should mark notification as read', () async {
      // Arrange
      final notification = NotificationModel(
        id: 1,
        recipientId: 123,
        message: 'Test',
        read: false,
        createdAt: [2024, 1, 15, 10, 30, 0, 0],
        type: 'TEST',
      );
      
      authProvider.notifications.add(notification);

      // Act
      final result = await authProvider.markNotificationAsRead(1);

      // Assert
      expect(result, true);
      expect(authProvider.notifications.first.read, true);
    });

    test('should mark all notifications as read', () async {
      // Arrange
      final notifications = [
        NotificationModel(id: 1, recipientId: 123, message: 'Test 1', read: false, createdAt: [2024, 1, 15, 10, 30, 0, 0], type: 'TEST'),
        NotificationModel(id: 2, recipientId: 123, message: 'Test 2', read: false, createdAt: [2024, 1, 15, 10, 30, 0, 0], type: 'TEST'),
      ];
      
      authProvider.notifications.addAll(notifications);

      // Act
      final result = await authProvider.markAllNotificationsAsRead();

      // Assert
      expect(result, true);
      expect(authProvider.notifications.every((n) => n.read), true);
    });

    test('should filter notifications by search query', () {
      // Arrange
      final notifications = [
        NotificationModel(id: 1, recipientId: 123, message: 'Delivery assigned', read: false, createdAt: [2024, 1, 15, 10, 30, 0, 0], type: 'DELIVERY_ASSIGNED'),
        NotificationModel(id: 2, recipientId: 123, message: 'Payment received', read: false, createdAt: [2024, 1, 15, 10, 30, 0, 0], type: 'PAYMENT_RECEIVED'),
      ];
      
      authProvider.notifications.addAll(notifications);

      // Act
      final filtered = authProvider.filterNotifications('delivery');

      // Assert
      expect(filtered.length, 1);
      expect(filtered.first.message, 'Delivery assigned');
    });

    test('should return unread notifications count', () {
      // Arrange
      final notifications = [
        NotificationModel(id: 1, recipientId: 123, message: 'Test 1', read: false, createdAt: [2024, 1, 15, 10, 30, 0, 0], type: 'TEST'),
        NotificationModel(id: 2, recipientId: 123, message: 'Test 2', read: true, createdAt: [2024, 1, 15, 10, 30, 0, 0], type: 'TEST'),
        NotificationModel(id: 3, recipientId: 123, message: 'Test 3', read: false, createdAt: [2024, 1, 15, 10, 30, 0, 0], type: 'TEST'),
      ];
      
      authProvider.notifications.addAll(notifications);

      // Act & Assert
      expect(authProvider.unreadNotificationsCount, 2);
    });
  });
}
